---
import { appLandingConfig } from '../config/landingPage.js';
import { Smartphone, Menu, X } from 'lucide-astro';

const { navigation, app } = appLandingConfig;
---

<nav class="navbar" id="navbar">
  <div class="nav-container">
    <div class="nav-logo">
      <a href="/" class="logo-link">
        <Smartphone class="logo-icon" />
        <span class="logo-text">{navigation.logo}</span>
      </a>
    </div>
    
    <div class="nav-menu" id="nav-menu">
      {navigation.links.map((link) => (
        <a href={link.href} class="nav-link" data-scroll-to={link.href.startsWith('#') ? link.href.slice(1) : null}>
          {link.text}
        </a>
      ))}
    </div>

    <button class="nav-toggle" id="nav-toggle" aria-label="Toggle navigation menu">
      <Menu class="menu-icon" />
      <X class="close-icon" />
    </button>
  </div>
</nav>

<style>
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(16px);
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 4px 32px rgba(102, 126, 234, 0.15);
    border-bottom-color: rgba(102, 126, 234, 0.2);
  }

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 72px;
  }

  .nav-logo {
    display: flex;
    align-items: center;
  }

  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #1a1a1a;
    font-weight: 700;
    font-size: 1.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 8px 12px;
    border-radius: 12px;
  }

  .logo-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-1px);
  }

  .logo-icon {
    width: 24px;
    height: 24px;
    margin-right: 8px;
    color: #667eea;
  }

  .nav-menu {
    display: flex;
    gap: 8px;
    align-items: center;
  }

  .nav-link {
    text-decoration: none;
    color: #4b5563;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 10px 16px;
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    white-space: nowrap;
  }

  .nav-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
  }

  .nav-link.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.15);
    font-weight: 600;
  }

  .nav-toggle {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .nav-toggle:hover {
    background: rgba(102, 126, 234, 0.1);
  }

  .menu-icon,
  .close-icon {
    width: 24px;
    height: 24px;
    color: #4b5563;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .close-icon {
    position: absolute;
    top: 8px;
    left: 8px;
    opacity: 0;
    transform: rotate(90deg);
  }

  .nav-toggle.active .menu-icon {
    opacity: 0;
    transform: rotate(-90deg);
  }

  .nav-toggle.active .close-icon {
    opacity: 1;
    transform: rotate(0deg);
  }

  /* Mobile Styles */
  @media (max-width: 768px) {
    .nav-container {
      padding: 0 20px;
      height: 64px;
    }

    .nav-menu {
      position: fixed;
      left: -100%;
      top: 64px;
      flex-direction: column;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(16px);
      width: 100%;
      text-align: center;
      transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.15);
      padding: 24px 0;
      gap: 8px;
      border-top: 1px solid rgba(102, 126, 234, 0.1);
    }

    .nav-menu.active {
      left: 0;
    }

    .nav-link {
      display: block;
      padding: 16px 24px;
      margin: 0 16px;
      border-radius: 12px;
      border-bottom: none;
      font-size: 1rem;
    }

    .nav-toggle {
      display: flex;
    }

    .logo-link {
      font-size: 1.1rem;
      padding: 6px 10px;
    }

    .logo-icon {
      width: 20px;
      height: 20px;
    }
  }

  @media (max-width: 480px) {
    .nav-container {
      padding: 0 16px;
    }

    .logo-link {
      font-size: 1rem;
    }
  }

  /* Smooth scroll offset for fixed navbar */
  :global(html) {
    scroll-padding-top: 88px;
  }

  @media (max-width: 768px) {
    :global(html) {
      scroll-padding-top: 80px;
    }
  }
</style>

<script>
  // Navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    navToggle?.addEventListener('click', function() {
      navMenu?.classList.toggle('active');
      navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
      link.addEventListener('click', function() {
        navMenu?.classList.remove('active');
        navToggle?.classList.remove('active');
      });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
      if (window.scrollY > 50) {
        navbar?.classList.add('scrolled');
      } else {
        navbar?.classList.remove('scrolled');
      }
    });

    // Smooth scrolling for anchor links
    navLinks.forEach(link => {
      const scrollTo = link.getAttribute('data-scroll-to');
      if (scrollTo) {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.getElementById(scrollTo);
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      }
    });

    // Active link highlighting based on scroll position
    const sections = document.querySelectorAll('section[id]');
    
    function highlightActiveLink() {
      const scrollPos = window.scrollY + 100;
      
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
          navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-scroll-to') === sectionId) {
              link.classList.add('active');
            }
          });
        }
      });
    }

    window.addEventListener('scroll', highlightActiveLink);
    highlightActiveLink(); // Initial call
  });
</script>
