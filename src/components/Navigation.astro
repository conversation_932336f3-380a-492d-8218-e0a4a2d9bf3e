---
import { appLandingConfig } from '../config/landingPage.js';

const { navigation } = appLandingConfig;
---

<nav class="navbar" id="navbar">
  <div class="nav-container">
    <div class="nav-logo">
      <a href="/" class="logo-link">
        <span class="logo-icon">📱</span>
        <span class="logo-text">{navigation.logo}</span>
      </a>
    </div>
    
    <div class="nav-menu" id="nav-menu">
      {navigation.links.map((link) => (
        <a href={link.href} class="nav-link" data-scroll-to={link.href.startsWith('#') ? link.href.slice(1) : null}>
          {link.text}
        </a>
      ))}
    </div>
    
    <div class="nav-toggle" id="nav-toggle">
      <span class="bar"></span>
      <span class="bar"></span>
      <span class="bar"></span>
    </div>
  </div>
</nav>

<style>
  .navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
  }

  .navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  }

  .nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
  }

  .nav-logo {
    display: flex;
    align-items: center;
  }

  .logo-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #1a1a1a;
    font-weight: 700;
    font-size: 1.2rem;
    transition: color 0.3s ease;
  }

  .logo-link:hover {
    color: #667eea;
  }

  .logo-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
  }

  .nav-menu {
    display: flex;
    gap: 2rem;
    align-items: center;
  }

  .nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
  }

  .nav-link:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
  }

  .nav-link.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.15);
  }

  .nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
  }

  .bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
    border-radius: 2px;
  }

  /* Mobile Styles */
  @media (max-width: 768px) {
    .nav-menu {
      position: fixed;
      left: -100%;
      top: 70px;
      flex-direction: column;
      background: rgba(255, 255, 255, 0.98);
      backdrop-filter: blur(10px);
      width: 100%;
      text-align: center;
      transition: 0.3s;
      box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
      padding: 2rem 0;
      gap: 0;
    }

    .nav-menu.active {
      left: 0;
    }

    .nav-link {
      display: block;
      padding: 1rem 2rem;
      margin: 0;
      border-radius: 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .nav-link:last-child {
      border-bottom: none;
    }

    .nav-toggle {
      display: flex;
    }

    .nav-toggle.active .bar:nth-child(2) {
      opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(1) {
      transform: translateY(8px) rotate(45deg);
    }

    .nav-toggle.active .bar:nth-child(3) {
      transform: translateY(-8px) rotate(-45deg);
    }
  }

  /* Smooth scroll offset for fixed navbar */
  :global(html) {
    scroll-padding-top: 80px;
  }
</style>

<script>
  // Navigation functionality
  document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('navbar');
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    const navLinks = document.querySelectorAll('.nav-link');

    // Mobile menu toggle
    navToggle?.addEventListener('click', function() {
      navMenu?.classList.toggle('active');
      navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on a link
    navLinks.forEach(link => {
      link.addEventListener('click', function() {
        navMenu?.classList.remove('active');
        navToggle?.classList.remove('active');
      });
    });

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
      if (window.scrollY > 50) {
        navbar?.classList.add('scrolled');
      } else {
        navbar?.classList.remove('scrolled');
      }
    });

    // Smooth scrolling for anchor links
    navLinks.forEach(link => {
      const scrollTo = link.getAttribute('data-scroll-to');
      if (scrollTo) {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          const target = document.getElementById(scrollTo);
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      }
    });

    // Active link highlighting based on scroll position
    const sections = document.querySelectorAll('section[id]');
    
    function highlightActiveLink() {
      const scrollPos = window.scrollY + 100;
      
      sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.offsetHeight;
        const sectionId = section.getAttribute('id');
        
        if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
          navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('data-scroll-to') === sectionId) {
              link.classList.add('active');
            }
          });
        }
      });
    }

    window.addEventListener('scroll', highlightActiveLink);
    highlightActiveLink(); // Initial call
  });
</script>
