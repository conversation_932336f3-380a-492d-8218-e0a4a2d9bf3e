---
export interface Props {
  buttons: {
    ios?: {
      text: string;
      href: string;
      icon: string;
      badge?: string;
    };
    android?: {
      text: string;
      href: string;
      icon: string;
      badge?: string;
    };
    web?: {
      text: string;
      href: string;
      icon: string;
    };
  };
  size?: 'normal' | 'large';
}

const { buttons, size = 'normal' } = Astro.props;
---

<div class={`download-buttons ${size}`}>
  {buttons.ios && (
    <a href={buttons.ios.href} class="download-btn ios-btn" target="_blank" rel="noopener noreferrer">
      {buttons.ios.badge ? (
        <img src={buttons.ios.badge} alt={buttons.ios.text} class="store-badge" />
      ) : (
        <>
          <span class="btn-icon">{buttons.ios.icon}</span>
          <div class="btn-text">
            <span class="btn-subtitle">Download on the</span>
            <span class="btn-title">App Store</span>
          </div>
        </>
      )}
    </a>
  )}
  
  {buttons.android && (
    <a href={buttons.android.href} class="download-btn android-btn" target="_blank" rel="noopener noreferrer">
      {buttons.android.badge ? (
        <img src={buttons.android.badge} alt={buttons.android.text} class="store-badge" />
      ) : (
        <>
          <span class="btn-icon">{buttons.android.icon}</span>
          <div class="btn-text">
            <span class="btn-subtitle">Get it on</span>
            <span class="btn-title">Google Play</span>
          </div>
        </>
      )}
    </a>
  )}
  
  {buttons.web && (
    <a href={buttons.web.href} class="download-btn web-btn" target="_blank" rel="noopener noreferrer">
      <span class="btn-icon">{buttons.web.icon}</span>
      <div class="btn-text">
        <span class="btn-title">{buttons.web.text}</span>
      </div>
    </a>
  )}
</div>

<style>
  .download-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin: 2rem 0;
  }

  .download-btn {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-width: 160px;
    border: 2px solid transparent;
  }

  .download-btn:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  }

  .ios-btn:hover {
    border-color: #007AFF;
    box-shadow: 0 10px 25px rgba(0, 122, 255, 0.3);
  }

  .android-btn:hover {
    border-color: #34A853;
    box-shadow: 0 10px 25px rgba(52, 168, 83, 0.3);
  }

  .web-btn {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
  }

  .web-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
  }

  .btn-icon {
    font-size: 2rem;
    margin-right: 12px;
  }

  .btn-text {
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .btn-subtitle {
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1;
  }

  .btn-title {
    font-size: 1.1rem;
    font-weight: 600;
    line-height: 1.2;
  }

  .store-badge {
    height: 50px;
    width: auto;
  }

  /* Large size variant */
  .download-buttons.large .download-btn {
    padding: 16px 24px;
    min-width: 180px;
  }

  .download-buttons.large .btn-icon {
    font-size: 2.5rem;
    margin-right: 16px;
  }

  .download-buttons.large .btn-subtitle {
    font-size: 0.9rem;
  }

  .download-buttons.large .btn-title {
    font-size: 1.2rem;
  }

  .download-buttons.large .store-badge {
    height: 60px;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .download-buttons {
      flex-direction: column;
      align-items: center;
      gap: 0.8rem;
    }

    .download-btn {
      width: 100%;
      max-width: 280px;
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .download-btn {
      padding: 14px 18px;
      min-width: 140px;
    }

    .btn-icon {
      font-size: 1.8rem;
      margin-right: 10px;
    }

    .btn-subtitle {
      font-size: 0.75rem;
    }

    .btn-title {
      font-size: 1rem;
    }

    .store-badge {
      height: 45px;
    }

    .download-buttons.large .download-btn {
      padding: 16px 20px;
    }

    .download-buttons.large .store-badge {
      height: 50px;
    }
  }

  /* Animation effects */
  .download-btn {
    position: relative;
    overflow: hidden;
  }

  .download-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .download-btn:hover::before {
    left: 100%;
  }
</style>
