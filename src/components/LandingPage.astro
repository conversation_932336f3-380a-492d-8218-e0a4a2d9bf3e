---
import { landingPageConfig } from '../config/landingPage.js';

const { hero, features, about, services, cta, contact, footer } = landingPageConfig;
---

<div class="landing-page">
  <!-- Hero Section -->
  <section id="hero" class="hero">
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">{hero.title}</h1>
        <h2 class="hero-subtitle">{hero.subtitle}</h2>
        <p class="hero-description">{hero.description}</p>
        <div class="hero-buttons">
          <a href={hero.primaryButton.href} class="btn btn-primary">{hero.primaryButton.text}</a>
          <a href={hero.secondaryButton.href} class="btn btn-secondary">{hero.secondaryButton.text}</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{features.title}</h2>
        <p class="section-subtitle">{features.subtitle}</p>
      </div>
      <div class="features-grid">
        {features.items.map((feature) => (
          <div class="feature-card">
            <div class="feature-icon">{feature.icon}</div>
            <h3 class="feature-title">{feature.title}</h3>
            <p class="feature-description">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- About Section -->
  <section id="about" class="about">
    <div class="container">
      <div class="about-content">
        <h2 class="section-title">{about.title}</h2>
        <p class="about-description">{about.description}</p>
        <div class="stats-grid">
          {about.stats.map((stat) => (
            <div class="stat-item">
              <div class="stat-number">{stat.number}</div>
              <div class="stat-label">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </section>

  <!-- Services Section -->
  <section id="services" class="services">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{services.title}</h2>
        <p class="section-subtitle">{services.subtitle}</p>
      </div>
      <div class="services-grid">
        {services.items.map((service) => (
          <div class="service-card">
            <h3 class="service-title">{service.title}</h3>
            <p class="service-description">{service.description}</p>
            <ul class="service-features">
              {service.features.map((feature) => (
                <li>{feature}</li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section id="cta" class="cta">
    <div class="container">
      <div class="cta-content">
        <h2 class="cta-title">{cta.title}</h2>
        <p class="cta-description">{cta.description}</p>
        <a href={cta.button.href} class="btn btn-primary btn-large">{cta.button.text}</a>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="contact-content">
        <h2 class="section-title">{contact.title}</h2>
        <p class="contact-description">{contact.description}</p>
        <div class="contact-info">
          <div class="contact-item">
            <strong>Email:</strong> <a href={`mailto:${contact.email}`}>{contact.email}</a>
          </div>
          <div class="contact-item">
            <strong>Phone:</strong> <a href={`tel:${contact.phone}`}>{contact.phone}</a>
          </div>
          <div class="contact-item">
            <strong>Address:</strong> {contact.address}
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <h3>{footer.companyName}</h3>
          <p>&copy; {new Date().getFullYear()} {footer.companyName}. {footer.copyright}</p>
        </div>
        <div class="footer-links">
          {footer.links.map((link) => (
            <a href={link.href}>{link.title}</a>
          ))}
        </div>
        <div class="footer-social">
          {footer.social.map((social) => (
            <a href={social.href} title={social.name} target="_blank" rel="noopener noreferrer">
              {social.icon}
            </a>
          ))}
        </div>
      </div>
    </div>
  </footer>
</div>

<style>
  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .landing-page {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  /* Typography */
  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    color: #1a1a1a;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-bottom: 3rem;
  }

  /* Buttons */
  .btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
  }

  .btn-secondary {
    background: transparent;
    color: #667eea;
    border-color: #667eea;
  }

  .btn-secondary:hover {
    background: #667eea;
    color: white;
  }

  .btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
  }

  /* Hero Section */
  .hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
  }

  .hero-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }

  .hero-description {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.8;
  }

  .hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  /* Features Section */
  .features {
    padding: 80px 0;
    background: #f8fafc;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
  }

  .feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
  }

  .feature-card:hover {
    transform: translateY(-5px);
  }

  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }

  .feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
  }

  .feature-description {
    color: #666;
    line-height: 1.6;
  }

  /* About Section */
  .about {
    padding: 80px 0;
  }

  .about-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .about-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 3rem;
    line-height: 1.8;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
  }

  .stat-item {
    text-align: center;
  }

  .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
  }

  .stat-label {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
  }

  /* Services Section */
  .services {
    padding: 80px 0;
    background: #f8fafc;
  }

  .services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
  }

  .service-card:hover {
    transform: translateY(-5px);
  }

  .service-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
  }

  .service-description {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }

  .service-features {
    list-style: none;
  }

  .service-features li {
    padding: 0.5rem 0;
    color: #667eea;
    font-weight: 500;
  }

  .service-features li:before {
    content: "✓ ";
    color: #10b981;
    font-weight: bold;
    margin-right: 0.5rem;
  }

  /* CTA Section */
  .cta {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
  }

  .cta-content {
    max-width: 600px;
    margin: 0 auto;
  }

  .cta-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
  }

  .cta-description {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
  }

  /* Contact Section */
  .contact {
    padding: 80px 0;
  }

  .contact-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .contact-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }

  .contact-item {
    font-size: 1.1rem;
  }

  .contact-item a {
    color: #667eea;
    text-decoration: none;
  }

  .contact-item a:hover {
    text-decoration: underline;
  }

  /* Footer */
  .footer {
    background: #1a1a1a;
    color: white;
    padding: 40px 0;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    align-items: center;
  }

  .footer-info h3 {
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
  }

  .footer-info p {
    color: #ccc;
    font-size: 0.9rem;
  }

  .footer-links {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
  }

  .footer-links a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  .footer-social {
    display: flex;
    gap: 1rem;
    justify-content: center;
  }

  .footer-social a {
    display: inline-block;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
  }

  .footer-social a:hover {
    transform: scale(1.2);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero-title {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1.2rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .hero-buttons {
      flex-direction: column;
      align-items: center;
    }

    .section-title {
      font-size: 2rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .services-grid {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
      grid-template-columns: 1fr;
      text-align: center;
    }

    .footer-links {
      justify-content: center;
    }

    .contact-info {
      text-align: left;
    }
  }

  @media (max-width: 480px) {
    .hero {
      padding: 60px 0;
    }

    .hero-title {
      font-size: 2rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .btn {
      width: 100%;
      text-align: center;
    }

    .hero-buttons .btn {
      width: auto;
      min-width: 200px;
    }
  }
</style>
