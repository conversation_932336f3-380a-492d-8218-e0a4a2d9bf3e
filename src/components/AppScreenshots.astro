---
import { appLandingConfig } from '../config/landingPage.js';

const { screenshots } = appLandingConfig;
---

<section id="screenshots" class="screenshots">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{screenshots.title}</h2>
      <p class="section-subtitle">{screenshots.subtitle}</p>
    </div>
    <div class="screenshots-container">
      <div class="screenshots-slider">
        {screenshots.images.map((screenshot) => (
          <div class="screenshot-item" data-animate="fade-up">
            <div class="screenshot-container">
              <img
                src={screenshot.src}
                alt={screenshot.alt}
                class="screenshot-image"
                loading="lazy"
              />
            </div>
            <p class="screenshot-caption">{screenshot.caption}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
</section>

<style>
  .screenshots {
    padding: 100px 0;
    background: white;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .section-title {
    font-size: 2.75rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-align: center;
    color: #111827;
    letter-spacing: -0.025em;
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .section-header {
    margin-bottom: 4rem;
  }

  .screenshots-container {
    overflow-x: auto;
    overflow-y: hidden;
    padding: 2rem 0;
    margin: 0 -24px;
    /* Hide scrollbar for cleaner appearance */
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .screenshots-container::-webkit-scrollbar {
    display: none;
  }

  .screenshots-slider {
    display: flex;
    gap: 2rem;
    padding: 0 24px;
    min-width: max-content;
    /* Smooth scroll behavior */
    scroll-behavior: smooth;
  }

  /* Add proper spacing for first and last items */
  .screenshots-slider .screenshot-item:first-child {
    margin-left: 0;
  }

  .screenshots-slider .screenshot-item:last-child {
    margin-right: 0;
    padding-right: 24px;
  }

  .screenshot-item {
    flex: 0 0 auto;
    text-align: center;
    max-width: 250px;
  }

  .screenshot-container {
    position: relative;
    width: 250px;
    height: auto;
    margin-bottom: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .screenshot-container:hover {
    transform: translateY(-4px) scale(1.01);
  }

  .screenshot-image {
    width: 100%;
    height: auto;
    display: block;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .screenshot-caption {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
    margin-top: 1rem;
  }

  /* Scrollbar styling */
  .screenshots-container::-webkit-scrollbar {
    height: 8px;
  }

  .screenshots-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  .screenshots-container::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
  }

  .screenshots-container::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 0 20px;
    }

    .screenshots-container {
      margin: 0 -20px;
    }

    .screenshots-slider {
      padding: 0 20px;
      gap: 1.75rem;
    }

    .screenshots-slider .screenshot-item:last-child {
      padding-right: 20px;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .screenshots {
      padding: 80px 0;
    }

    .section-title {
      font-size: 2.25rem;
    }

    .section-subtitle {
      font-size: 1.1rem;
      margin-bottom: 3rem;
    }

    .section-header {
      margin-bottom: 3rem;
    }

    .screenshots-container {
      margin: 0 -16px;
    }

    .screenshots-slider {
      gap: 1.5rem;
      padding: 0 16px;
    }

    .screenshots-slider .screenshot-item:last-child {
      padding-right: 16px;
    }

    .screenshot-container {
      width: 200px;
    }

    .screenshot-item {
      max-width: 200px;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .section-title {
      font-size: 1.875rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }

    .screenshots-container {
      margin: 0 -12px;
    }

    .screenshots-slider {
      gap: 1.25rem;
      padding: 0 12px;
    }

    .screenshots-slider .screenshot-item:last-child {
      padding-right: 12px;
    }

    .screenshot-container {
      width: 180px;
    }

    .screenshot-item {
      max-width: 180px;
    }

    .screenshot-caption {
      font-size: 0.9rem;
    }
  }

  /* Animation for scroll reveal */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }
</style>

<script>
  // Scroll animations for screenshots
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.screenshot-item[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          // Add staggered animation delay
          setTimeout(() => {
            entry.target.classList.add('animate');
          }, index * 100);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
