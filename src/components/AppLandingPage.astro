---
import { appLandingConfig } from '../config/landingPage.js';
import Navigation from './Navigation.astro';
import AppScreenshots from './AppScreenshots.astro';
import DownloadButtons from './DownloadButtons.astro';
import Testimonials from './Testimonials.astro';
import FAQ from './FAQ.astro';
import AppIcon from './AppIcon.astro';
import { Brain, RefreshCw, Users, BarChart3, WifiOff, Bell, Twitter, Instagram, Linkedin, Youtube } from 'lucide-astro';

const { app, hero, features, stats, download, contact, footer } = appLandingConfig;

// Icon mapping for dynamic icon rendering
const iconMap = {
  'Brain': Brain,
  'RefreshCw': RefreshCw,
  'Users': Users,
  'BarChart3': BarChart3,
  'WifiOff': WifiOff,
  'Bell': Bell
};

const socialIconMap = {
  'Twitter': Twitter,
  'Instagram': Instagram,
  'Linkedin': Linkedin,
  'Youtube': Youtube
};
---

<div class="app-landing">
  <Navigation />

  <!-- Hero Section -->
  <section id="hero" class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <AppIcon iconConfig={app.icon} size="xlarge" className="hero-app-icon" />
          <h1 class="hero-title">{hero.title}</h1>
          <h2 class="hero-subtitle">{hero.subtitle}</h2>
          <p class="hero-description">{hero.description}</p>
          <DownloadButtons buttons={hero.downloadButtons} />
        </div>
        <div class="hero-image">
          <img src={hero.heroImage} alt="TaskFlow Pro App Preview" class="hero-mockup" />
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{features.title}</h2>
        <p class="section-subtitle">{features.subtitle}</p>
      </div>
      <div class="features-grid">
        {features.items.map((feature) => {
          const IconComponent = iconMap[feature.icon];
          return (
            <div class="feature-card" data-animate="fade-up">
              <div class="feature-icon-wrapper">
                {IconComponent && <IconComponent class="feature-icon" />}
              </div>
              <h3 class="feature-title">{feature.title}</h3>
              <p class="feature-description">{feature.description}</p>
            </div>
          );
        })}
      </div>
    </div>
  </section>

  <!-- App Screenshots -->
  <AppScreenshots />

  <!-- Stats Section -->
  <section id="stats" class="stats">
    <div class="container">
      <h2 class="section-title">{stats.title}</h2>
      <div class="stats-grid">
        {stats.items.map((stat) => (
          <div class="stat-item" data-animate="fade-up">
            <div class="stat-number">{stat.number}</div>
            <div class="stat-label">{stat.label}</div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Testimonials -->
  <Testimonials />

  <!-- FAQ Section -->
  <FAQ />

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="contact-content">
        <h2 class="section-title">{contact.title}</h2>
        <p class="contact-description">{contact.description}</p>
        <div class="contact-info">
          <div class="contact-item">
            <strong>Email Support:</strong> 
            <a href={`mailto:${contact.email}`}>{contact.email}</a>
          </div>
          <div class="contact-item">
            <strong>Help Center:</strong> 
            <a href={contact.supportUrl} target="_blank" rel="noopener noreferrer">Visit Help Center</a>
          </div>
          <div class="contact-item">
            <strong>Social Support:</strong> 
            <span>{contact.socialSupport}</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <h3>{footer.appName}</h3>
          <p class="footer-tagline">{footer.tagline}</p>
          <p class="footer-copyright">
            &copy; {footer.legal.year} {footer.legal.company}. {footer.copyright}
          </p>
          <p class="footer-address">{footer.legal.address}</p>
        </div>
        <div class="footer-links">
          <h4>Links</h4>
          {footer.links.map((link) => (
            <a href={link.href}>{link.title}</a>
          ))}
        </div>
        <div class="footer-social">
          <h4>Follow Us</h4>
          <div class="social-links">
            {footer.social.map((social) => {
              const SocialIcon = socialIconMap[social.icon];
              return (
                <a href={social.href} title={social.name} target="_blank" rel="noopener noreferrer">
                  {SocialIcon && <SocialIcon class="social-icon" />}
                </a>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<style>
  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .app-landing {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    overflow-x: hidden;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* Typography */
  .section-title {
    font-size: 2.75rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-align: center;
    color: #111827;
    letter-spacing: -0.025em;
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .section-header {
    margin-bottom: 4rem;
  }

  /* Hero Section */
  .hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 100px;
    margin-top: 72px;
    min-height: calc(100vh - 72px);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
  }

  .hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    pointer-events: none;
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 5rem;
    align-items: center;
    position: relative;
    z-index: 1;
  }

  .hero-text {
    text-align: left;
  }

  /* Hero App Icon Styling */
  .hero-app-icon {
    margin-bottom: 2rem;
  }

  .hero-app-icon :global(.app-icon-wrapper) {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .hero-app-icon :global(.app-icon) {
    color: white;
  }

  .hero-title {
    font-size: 3.75rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.025em;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    font-weight: 500;
    margin-bottom: 2rem;
    opacity: 0.9;
    color: rgba(255, 255, 255, 0.9);
  }

  .hero-description {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    opacity: 0.85;
    line-height: 1.7;
    max-width: 500px;
  }

  .hero-image {
    text-align: center;
  }

  .hero-mockup {
    max-width: 100%;
    height: auto;
    max-height: 600px;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Features Section */
  .features {
    padding: 100px 0;
    background: #f8fafc;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
  }

  .feature-card {
    background: white;
    padding: 3rem 2.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(102, 126, 234, 0.08);
    position: relative;
    overflow: hidden;
  }

  .feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .feature-card:hover {
    transform: translateY(-12px);
    box-shadow: 0 25px 50px rgba(102, 126, 234, 0.15);
    border-color: rgba(102, 126, 234, 0.2);
  }

  .feature-card:hover::before {
    transform: scaleX(1);
  }

  .feature-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px;
    margin-bottom: 2rem;
    border: 1px solid rgba(102, 126, 234, 0.15);
  }

  .feature-icon {
    width: 36px;
    height: 36px;
    color: #667eea;
  }

  .feature-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.25rem;
    color: #111827;
    letter-spacing: -0.025em;
  }

  .feature-description {
    color: #6b7280;
    line-height: 1.7;
    font-size: 1rem;
  }

  /* Stats Section */
  .stats {
    padding: 80px 0;
    background: white;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
  }

  .stat-item {
    text-align: center;
    padding: 2rem;
  }

  .stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
    display: block;
  }

  .stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
  }

  /* Download Section */
  .download-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
  }

  .download-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .download-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
  }

  .qr-code {
    margin-top: 3rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qr-image {
    width: 150px;
    height: 150px;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .qr-text {
    font-size: 0.9rem;
    opacity: 0.8;
  }

  /* Contact Section */
  .contact {
    padding: 80px 0;
    background: #f8fafc;
  }

  .contact-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .contact-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .contact-item {
    font-size: 1.1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 400px;
  }

  .contact-item a {
    color: #667eea;
    text-decoration: none;
    margin-left: 0.5rem;
  }

  .contact-item a:hover {
    text-decoration: underline;
  }

  /* Footer */
  .footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 30px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
  }

  .footer-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .footer-tagline {
    color: #ccc;
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .footer-copyright {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .footer-address {
    color: #999;
    font-size: 0.8rem;
  }

  .footer-links h4,
  .footer-social h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .footer-links a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .social-links a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .social-links a:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .social-icon {
    width: 20px;
    height: 20px;
    color: white;
  }

  /* Animations */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 0 20px;
    }

    .hero-content {
      gap: 4rem;
    }

    .features-grid {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 2rem;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .hero {
      padding: 100px 0 80px;
      margin-top: 64px;
      min-height: calc(100vh - 64px);
    }

    .hero-content {
      grid-template-columns: 1fr;
      gap: 3rem;
      text-align: center;
    }

    .hero-text {
      text-align: center;
    }

    .hero-title {
      font-size: 2.75rem;
    }

    .hero-subtitle {
      font-size: 1.25rem;
    }

    .hero-description {
      font-size: 1.1rem;
      max-width: none;
    }

    .section-title {
      font-size: 2.25rem;
    }

    .section-subtitle {
      font-size: 1.1rem;
      margin-bottom: 3rem;
    }

    .features {
      padding: 80px 0;
    }

    .features-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .feature-card {
      padding: 2.5rem 2rem;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 3rem;
    }

    .contact-info {
      text-align: left;
    }

    .social-links {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .hero {
      padding: 80px 0 60px;
    }

    .hero-title {
      font-size: 2.25rem;
    }

    .hero-subtitle {
      font-size: 1.1rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .app-icon-wrapper {
      width: 64px;
      height: 64px;
      margin-bottom: 1.5rem;
    }

    .app-icon {
      width: 32px;
      height: 32px;
    }

    .section-title {
      font-size: 1.875rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .feature-card {
      padding: 2rem 1.5rem;
    }

    .feature-icon-wrapper {
      width: 64px;
      height: 64px;
    }

    .feature-icon {
      width: 28px;
      height: 28px;
    }

    .qr-code {
      margin-top: 2rem;
    }

    .qr-image {
      width: 120px;
      height: 120px;
    }

    .social-links {
      gap: 0.75rem;
    }

    .social-links a {
      width: 36px;
      height: 36px;
    }

    .social-icon {
      width: 18px;
      height: 18px;
    }
  }
</style>

<script>
  // Scroll animations
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('[data-animate]');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
