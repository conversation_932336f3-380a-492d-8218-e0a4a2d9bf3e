---
import { appLandingConfig } from '../config/landingPage.js';
import Navigation from './Navigation.astro';
import AppScreenshots from './AppScreenshots.astro';
import DownloadButtons from './DownloadButtons.astro';
import Testimonials from './Testimonials.astro';
import FAQ from './FAQ.astro';

const { hero, features, stats, download, contact, footer } = appLandingConfig;
---

<div class="app-landing">
  <Navigation />
  
  <!-- Hero Section -->
  <section id="hero" class="hero">
    <div class="container">
      <div class="hero-content">
        <div class="hero-text">
          <div class="app-icon">{hero.appIcon}</div>
          <h1 class="hero-title">{hero.title}</h1>
          <h2 class="hero-subtitle">{hero.subtitle}</h2>
          <p class="hero-description">{hero.description}</p>
          <DownloadButtons buttons={hero.downloadButtons} />
        </div>
        <div class="hero-image">
          <img src={hero.heroImage} alt="TaskFlow Pro App Preview" class="hero-mockup" />
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section id="features" class="features">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{features.title}</h2>
        <p class="section-subtitle">{features.subtitle}</p>
      </div>
      <div class="features-grid">
        {features.items.map((feature) => (
          <div class="feature-card" data-animate="fade-up">
            <div class="feature-icon">{feature.icon}</div>
            <h3 class="feature-title">{feature.title}</h3>
            <p class="feature-description">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- App Screenshots -->
  <AppScreenshots />

  <!-- Stats Section -->
  <section id="stats" class="stats">
    <div class="container">
      <h2 class="section-title">{stats.title}</h2>
      <div class="stats-grid">
        {stats.items.map((stat) => (
          <div class="stat-item" data-animate="fade-up">
            <div class="stat-number">{stat.number}</div>
            <div class="stat-label">{stat.label}</div>
          </div>
        ))}
      </div>
    </div>
  </section>

  <!-- Testimonials -->
  <Testimonials />

  <!-- FAQ Section -->
  <FAQ />

  <!-- Download Section -->
  <section id="download" class="download-section">
    <div class="container">
      <div class="download-content">
        <h2 class="section-title">{download.title}</h2>
        <p class="section-subtitle">{download.subtitle}</p>
        <p class="download-description">{download.description}</p>
        <DownloadButtons buttons={download.buttons} size="large" />
        <div class="qr-code">
          <img src={download.qrCode.image} alt="QR Code to download app" class="qr-image" />
          <p class="qr-text">{download.qrCode.text}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section id="contact" class="contact">
    <div class="container">
      <div class="contact-content">
        <h2 class="section-title">{contact.title}</h2>
        <p class="contact-description">{contact.description}</p>
        <div class="contact-info">
          <div class="contact-item">
            <strong>Email Support:</strong> 
            <a href={`mailto:${contact.email}`}>{contact.email}</a>
          </div>
          <div class="contact-item">
            <strong>Help Center:</strong> 
            <a href={contact.supportUrl} target="_blank" rel="noopener noreferrer">Visit Help Center</a>
          </div>
          <div class="contact-item">
            <strong>Social Support:</strong> 
            <span>{contact.socialSupport}</span>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <h3>{footer.appName}</h3>
          <p class="footer-tagline">{footer.tagline}</p>
          <p class="footer-copyright">
            &copy; {footer.legal.year} {footer.legal.company}. {footer.copyright}
          </p>
          <p class="footer-address">{footer.legal.address}</p>
        </div>
        <div class="footer-links">
          <h4>Links</h4>
          {footer.links.map((link) => (
            <a href={link.href}>{link.title}</a>
          ))}
        </div>
        <div class="footer-social">
          <h4>Follow Us</h4>
          <div class="social-links">
            {footer.social.map((social) => (
              <a href={social.href} title={social.name} target="_blank" rel="noopener noreferrer">
                {social.icon}
              </a>
            ))}
          </div>
        </div>
      </div>
    </div>
  </footer>
</div>

<style>
  /* Reset and base styles */
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  .app-landing {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  /* Typography */
  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    color: #1a1a1a;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-bottom: 3rem;
  }

  /* Hero Section */
  .hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 70px;
    min-height: 100vh;
    display: flex;
    align-items: center;
  }

  .hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
  }

  .hero-text {
    text-align: left;
  }

  .app-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    display: block;
  }

  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 1rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    margin-bottom: 1.5rem;
    opacity: 0.9;
  }

  .hero-description {
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    opacity: 0.8;
    line-height: 1.6;
  }

  .hero-image {
    text-align: center;
  }

  .hero-mockup {
    max-width: 100%;
    height: auto;
    max-height: 600px;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.3));
    animation: float 6s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  /* Features Section */
  .features {
    padding: 80px 0;
    background: #f8fafc;
  }

  .features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }

  .feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
  }

  .feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #1a1a1a;
  }

  .feature-description {
    color: #666;
    line-height: 1.6;
  }

  /* Stats Section */
  .stats {
    padding: 80px 0;
    background: white;
  }

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
  }

  .stat-item {
    text-align: center;
    padding: 2rem;
  }

  .stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 0.5rem;
    display: block;
  }

  .stat-label {
    font-size: 1.1rem;
    color: #666;
    font-weight: 500;
  }

  /* Download Section */
  .download-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
  }

  .download-content {
    max-width: 800px;
    margin: 0 auto;
  }

  .download-description {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
  }

  .qr-code {
    margin-top: 3rem;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .qr-image {
    width: 150px;
    height: 150px;
    background: white;
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1rem;
  }

  .qr-text {
    font-size: 0.9rem;
    opacity: 0.8;
  }

  /* Contact Section */
  .contact {
    padding: 80px 0;
    background: #f8fafc;
  }

  .contact-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .contact-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 2rem;
  }

  .contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    align-items: center;
  }

  .contact-item {
    font-size: 1.1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 400px;
  }

  .contact-item a {
    color: #667eea;
    text-decoration: none;
    margin-left: 0.5rem;
  }

  .contact-item a:hover {
    text-decoration: underline;
  }

  /* Footer */
  .footer {
    background: #1a1a1a;
    color: white;
    padding: 60px 0 30px;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
  }

  .footer-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .footer-tagline {
    color: #ccc;
    font-size: 1rem;
    margin-bottom: 1rem;
  }

  .footer-copyright {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
  }

  .footer-address {
    color: #999;
    font-size: 0.8rem;
  }

  .footer-links h4,
  .footer-social h4 {
    margin-bottom: 1rem;
    font-size: 1.1rem;
  }

  .footer-links a {
    display: block;
    color: #ccc;
    text-decoration: none;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
  }

  .footer-links a:hover {
    color: white;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .social-links a {
    display: inline-block;
    font-size: 1.5rem;
    transition: transform 0.3s ease;
  }

  .social-links a:hover {
    transform: scale(1.2);
  }

  /* Animations */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .hero {
      padding: 100px 0 60px;
      min-height: auto;
    }

    .hero-content {
      grid-template-columns: 1fr;
      gap: 2rem;
      text-align: center;
    }

    .hero-text {
      text-align: center;
    }

    .hero-title {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 1.2rem;
    }

    .hero-description {
      font-size: 1rem;
    }

    .section-title {
      font-size: 2rem;
    }

    .features-grid {
      grid-template-columns: 1fr;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
      grid-template-columns: 1fr;
      text-align: center;
      gap: 2rem;
    }

    .contact-info {
      text-align: left;
    }

    .social-links {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .hero {
      padding: 80px 0 40px;
    }

    .hero-title {
      font-size: 2rem;
    }

    .stats-grid {
      grid-template-columns: 1fr;
    }

    .app-icon {
      font-size: 3rem;
    }

    .feature-card {
      padding: 2rem;
    }

    .qr-code {
      margin-top: 2rem;
    }

    .qr-image {
      width: 120px;
      height: 120px;
    }
  }
</style>

<script>
  // Scroll animations
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('[data-animate]');

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate');
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
