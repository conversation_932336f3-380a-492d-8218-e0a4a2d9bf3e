---
import { appLandingConfig } from '../config/landingPage.js';

const { testimonials } = appLandingConfig;
---

<section id="testimonials" class="testimonials">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{testimonials.title}</h2>
      <p class="section-subtitle">{testimonials.subtitle}</p>
    </div>
    <div class="testimonials-grid">
      {testimonials.items.map((testimonial) => (
        <div class="testimonial-card" data-animate="fade-up">
          <div class="testimonial-content">
            <div class="stars">
              {Array.from({ length: 5 }, (_, i) => (
                <span class={`star ${i < testimonial.rating ? 'filled' : ''}`}>★</span>
              ))}
            </div>
            <p class="testimonial-text">"{testimonial.text}"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar-wrapper">
              <img
                src={testimonial.avatar}
                alt={`${testimonial.name} avatar`}
                class="author-avatar"
                loading="lazy"
              />
            </div>
            <div class="author-info">
              <h4 class="author-name">{testimonial.name}</h4>
              <p class="author-role">{testimonial.role}</p>
              <p class="author-company">{testimonial.company}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>

<style>
  .testimonials {
    padding: 100px 0;
    background: #f8fafc;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  .section-title {
    font-size: 2.75rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-align: center;
    color: #111827;
    letter-spacing: -0.025em;
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: #6b7280;
    text-align: center;
    margin-bottom: 4rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
  }

  .section-header {
    margin-bottom: 4rem;
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    /* Ensure proper spacing around the grid */
    margin: 0 auto;
    max-width: 100%;
  }

  .testimonial-card {
    background: white;
    border-radius: 24px;
    padding: 2.5rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 320px;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  .testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .testimonial-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    pointer-events: none;
  }

  .testimonial-card:hover {
    transform: translateY(-16px) scale(1.02);
    box-shadow: 0 32px 64px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.25);
  }

  .testimonial-card:hover::before {
    transform: scaleX(1);
  }

  .testimonial-card:hover::after {
    opacity: 1;
  }

  .testimonial-content {
    flex-grow: 1;
    margin-bottom: 1.5rem;
  }

  .stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
    justify-content: flex-start;
  }

  .star {
    font-size: 1.25rem;
    color: #e5e7eb;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .star.filled {
    color: #fbbf24;
    text-shadow: 0 1px 3px rgba(251, 191, 36, 0.3);
  }

  .testimonial-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #374151;
    font-style: italic;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
  }

  .testimonial-text::before {
    content: '"';
    font-size: 4rem;
    color: rgba(102, 126, 234, 0.1);
    position: absolute;
    top: -1rem;
    left: -0.5rem;
    font-family: Georgia, serif;
    line-height: 1;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
  }

  .author-avatar-wrapper {
    position: relative;
    flex-shrink: 0;
  }

  .author-avatar-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    z-index: -1;
  }

  .author-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    position: relative;
    z-index: 1;
  }

  .author-info {
    flex-grow: 1;
  }

  .author-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.2rem;
  }

  .author-role {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.1rem;
  }

  .author-company {
    font-size: 0.8rem;
    color: #6b7280;
  }

  /* Animation for scroll reveal */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .container {
      padding: 0 20px;
    }

    .testimonials-grid {
      gap: 2rem;
    }
  }

  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .testimonials {
      padding: 80px 0;
    }

    .section-title {
      font-size: 2.25rem;
    }

    .section-subtitle {
      font-size: 1.1rem;
      margin-bottom: 3rem;
    }

    .section-header {
      margin-bottom: 3rem;
    }

    .testimonials-grid {
      grid-template-columns: 1fr;
      gap: 2rem;
    }

    .testimonial-card {
      padding: 2rem;
      min-height: auto;
    }

    .testimonial-text {
      font-size: 0.95rem;
    }

    .author-avatar {
      width: 45px;
      height: 45px;
    }

    .author-name {
      font-size: 0.95rem;
    }

    .author-role {
      font-size: 0.85rem;
    }

    .author-company {
      font-size: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .section-title {
      font-size: 1.875rem;
    }

    .section-subtitle {
      font-size: 1rem;
    }

    .testimonials-grid {
      gap: 1.5rem;
    }

    .testimonial-card {
      padding: 1.5rem;
    }

    .testimonial-text {
      font-size: 0.9rem;
      margin-bottom: 1rem;
    }

    .author-avatar {
      width: 40px;
      height: 40px;
    }

    .stars {
      margin-bottom: 0.8rem;
    }

    .star {
      font-size: 1rem;
    }
  }

  /* Hover effects for stars */
  .testimonial-card:hover .star.filled {
    color: #f59e0b;
    transform: scale(1.1);
  }

  /* Loading animation for avatars */
  .author-avatar {
    background: #f3f4f6;
    position: relative;
    overflow: hidden;
  }

  .author-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
</style>

<script>
  // Scroll animations for testimonials
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.testimonial-card[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          // Add staggered animation delay
          setTimeout(() => {
            entry.target.classList.add('animate');
          }, index * 150);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
