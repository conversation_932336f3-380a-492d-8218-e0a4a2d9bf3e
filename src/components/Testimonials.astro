---
import { appLandingConfig } from '../config/landingPage.js';

const { testimonials } = appLandingConfig;
---

<section id="testimonials" class="testimonials">
  <div class="container">
    <div class="section-header">
      <h2 class="section-title">{testimonials.title}</h2>
      <p class="section-subtitle">{testimonials.subtitle}</p>
    </div>
    <div class="testimonials-grid">
      {testimonials.items.map((testimonial, index) => (
        <div class="testimonial-card" data-animate="fade-up">
          <div class="testimonial-content">
            <div class="stars">
              {Array.from({ length: 5 }, (_, i) => (
                <span class={`star ${i < testimonial.rating ? 'filled' : ''}`}>★</span>
              ))}
            </div>
            <p class="testimonial-text">"{testimonial.text}"</p>
          </div>
          <div class="testimonial-author">
            <img 
              src={testimonial.avatar} 
              alt={`${testimonial.name} avatar`}
              class="author-avatar"
              loading="lazy"
            />
            <div class="author-info">
              <h4 class="author-name">{testimonial.name}</h4>
              <p class="author-role">{testimonial.role}</p>
              <p class="author-company">{testimonial.company}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
</section>

<style>
  .testimonials {
    padding: 80px 0;
    background: #f8fafc;
  }

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
    color: #1a1a1a;
  }

  .section-subtitle {
    font-size: 1.2rem;
    color: #666;
    text-align: center;
    margin-bottom: 3rem;
  }

  .testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .testimonial-card {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 280px;
  }

  .testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .testimonial-content {
    flex-grow: 1;
    margin-bottom: 1.5rem;
  }

  .stars {
    display: flex;
    gap: 0.2rem;
    margin-bottom: 1rem;
  }

  .star {
    font-size: 1.2rem;
    color: #e5e7eb;
    transition: color 0.3s ease;
  }

  .star.filled {
    color: #fbbf24;
  }

  .testimonial-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #374151;
    font-style: italic;
    margin-bottom: 1.5rem;
  }

  .testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
  }

  .author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
  }

  .author-info {
    flex-grow: 1;
  }

  .author-name {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.2rem;
  }

  .author-role {
    font-size: 0.9rem;
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.1rem;
  }

  .author-company {
    font-size: 0.8rem;
    color: #6b7280;
  }

  /* Animation for scroll reveal */
  [data-animate="fade-up"] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
  }

  [data-animate="fade-up"].animate {
    opacity: 1;
    transform: translateY(0);
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .testimonials-grid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .testimonial-card {
      padding: 1.5rem;
      min-height: auto;
    }

    .testimonial-text {
      font-size: 0.95rem;
    }

    .author-avatar {
      width: 45px;
      height: 45px;
    }

    .author-name {
      font-size: 0.95rem;
    }

    .author-role {
      font-size: 0.85rem;
    }

    .author-company {
      font-size: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .testimonial-card {
      padding: 1.25rem;
    }

    .testimonial-text {
      font-size: 0.9rem;
      margin-bottom: 1rem;
    }

    .author-avatar {
      width: 40px;
      height: 40px;
    }

    .stars {
      margin-bottom: 0.8rem;
    }

    .star {
      font-size: 1rem;
    }
  }

  /* Hover effects for stars */
  .testimonial-card:hover .star.filled {
    color: #f59e0b;
    transform: scale(1.1);
  }

  /* Loading animation for avatars */
  .author-avatar {
    background: #f3f4f6;
    position: relative;
    overflow: hidden;
  }

  .author-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }
</style>

<script>
  // Scroll animations for testimonials
  document.addEventListener('DOMContentLoaded', function() {
    const animateElements = document.querySelectorAll('.testimonial-card[data-animate]');
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry, index) => {
        if (entry.isIntersecting) {
          // Add staggered animation delay
          setTimeout(() => {
            entry.target.classList.add('animate');
          }, index * 150);
        }
      });
    }, {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    });

    animateElements.forEach(el => {
      observer.observe(el);
    });
  });
</script>
