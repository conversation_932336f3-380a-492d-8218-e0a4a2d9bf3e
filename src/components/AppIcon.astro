---
import { Smartphone, CheckSquare, Calendar, Target, Zap, Star, Heart, Award, Brain, Refresh<PERSON>w, Users, BarChart3, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-astro';

export interface Props {
  iconConfig: {
    type: 'lucide' | 'image';
    value: string;
  };
  size?: 'small' | 'medium' | 'large' | 'xlarge';
  className?: string;
}

const { iconConfig, size = 'medium', className = '' } = Astro.props;

// Icon mapping for Lucide icons
const iconMap = {
  'Smartphone': Smartphone,
  'CheckSquare': CheckSquare,
  'Calendar': Calendar,
  'Target': Target,
  'Zap': Zap,
  'Star': Star,
  'Heart': Heart,
  'Award': Award,
  'Brain': Brain,
  'RefreshCw': RefreshCw,
  'Users': Users,
  'BarChart3': BarChart3,
  'WifiOff': WifiOff,
  'Bell': Bell
};

// Size configurations
const sizeClasses = {
  small: 'app-icon-small',
  medium: 'app-icon-medium', 
  large: 'app-icon-large',
  xlarge: 'app-icon-xlarge'
};

const IconComponent = iconConfig.type === 'lucide' ? iconMap[iconConfig.value] : null;
---

<div class={`app-icon-wrapper ${sizeClasses[size]} ${className}`}>
  {iconConfig.type === 'lucide' && IconComponent ? (
    <IconComponent class="app-icon" />
  ) : iconConfig.type === 'image' ? (
    <img 
      src={iconConfig.value} 
      alt="App Icon" 
      class="app-icon app-icon-image"
      loading="lazy"
    />
  ) : (
    <!-- Fallback icon -->
    <Smartphone class="app-icon" />
  )}
</div>

<style>
  .app-icon-wrapper {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
  }

  .app-icon-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .app-icon-wrapper:hover::before {
    opacity: 1;
  }

  .app-icon {
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
  }

  .app-icon-image {
    border-radius: 16px;
    object-fit: cover;
  }

  /* Size variants */
  .app-icon-small {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }

  .app-icon-small .app-icon {
    width: 24px;
    height: 24px;
  }

  .app-icon-small .app-icon-image {
    width: 32px;
    height: 32px;
    border-radius: 8px;
  }

  .app-icon-medium {
    width: 64px;
    height: 64px;
    border-radius: 16px;
  }

  .app-icon-medium .app-icon {
    width: 32px;
    height: 32px;
  }

  .app-icon-medium .app-icon-image {
    width: 48px;
    height: 48px;
    border-radius: 12px;
  }

  .app-icon-large {
    width: 80px;
    height: 80px;
    border-radius: 20px;
  }

  .app-icon-large .app-icon {
    width: 40px;
    height: 40px;
  }

  .app-icon-large .app-icon-image {
    width: 64px;
    height: 64px;
    border-radius: 16px;
  }

  .app-icon-xlarge {
    width: 120px;
    height: 120px;
    border-radius: 24px;
  }

  .app-icon-xlarge .app-icon {
    width: 60px;
    height: 60px;
  }

  .app-icon-xlarge .app-icon-image {
    width: 96px;
    height: 96px;
    border-radius: 20px;
  }

  /* Responsive adjustments */
  @media (max-width: 768px) {
    .app-icon-xlarge {
      width: 96px;
      height: 96px;
    }

    .app-icon-xlarge .app-icon {
      width: 48px;
      height: 48px;
    }

    .app-icon-xlarge .app-icon-image {
      width: 80px;
      height: 80px;
    }
  }

  @media (max-width: 480px) {
    .app-icon-large {
      width: 64px;
      height: 64px;
    }

    .app-icon-large .app-icon {
      width: 32px;
      height: 32px;
    }

    .app-icon-large .app-icon-image {
      width: 48px;
      height: 48px;
    }

    .app-icon-xlarge {
      width: 80px;
      height: 80px;
    }

    .app-icon-xlarge .app-icon {
      width: 40px;
      height: 40px;
    }

    .app-icon-xlarge .app-icon-image {
      width: 64px;
      height: 64px;
    }
  }
</style>
