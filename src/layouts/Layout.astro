---
export interface Props {
	title?: string;
	description?: string;
	keywords?: string;
	ogImage?: string;
	ogType?: string;
	twitterCard?: string;
	canonicalUrl?: string;
	appName?: string;
	appCategory?: string;
	appRating?: number;
	structuredData?: object;
}

const {
	title = "TaskFlow Pro - The Ultimate Productivity App",
	description = "Download TaskFlow Pro and transform how you manage tasks. Available on iOS and Android with smart automation, beautiful design, and powerful features.",
	keywords = "task management app, productivity app, todo list app, project management, mobile app, ios app, android app",
	ogImage = "/images/app-social-preview.jpg",
	ogType = "website",
	twitterCard = "summary_large_image",
	canonicalUrl = Astro.url.href,
	appName = "TaskFlow Pro",
	appCategory = "Productivity",
	appRating = 4.8,
	structuredData = null
} = Astro.props;

// Generate structured data for mobile app
const defaultStructuredData = {
	"@context": "https://schema.org",
	"@type": "MobileApplication",
	"name": appName,
	"applicationCategory": appCategory,
	"operatingSystem": ["iOS", "Android"],
	"aggregateRating": {
		"@type": "AggregateRating",
		"ratingValue": appRating,
		"ratingCount": "12500"
	},
	"offers": {
		"@type": "Offer",
		"price": "0",
		"priceCurrency": "USD"
	}
};

const finalStructuredData = structuredData || defaultStructuredData;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />

		<!-- Basic SEO -->
		<title>{title}</title>
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<link rel="canonical" href={canonicalUrl} />

		<!-- Open Graph / Facebook -->
		<meta property="og:type" content={ogType} />
		<meta property="og:url" content={canonicalUrl} />
		<meta property="og:title" content={title} />
		<meta property="og:description" content={description} />
		<meta property="og:image" content={ogImage} />
		<meta property="og:site_name" content={appName} />

		<!-- Twitter -->
		<meta property="twitter:card" content={twitterCard} />
		<meta property="twitter:url" content={canonicalUrl} />
		<meta property="twitter:title" content={title} />
		<meta property="twitter:description" content={description} />
		<meta property="twitter:image" content={ogImage} />

		<!-- Mobile App Meta Tags -->
		<meta name="apple-mobile-web-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="default" />
		<meta name="apple-mobile-web-app-title" content={appName} />
		<meta name="mobile-web-app-capable" content="yes" />
		<meta name="application-name" content={appName} />

		<!-- Theme Colors -->
		<meta name="theme-color" content="#667eea" />
		<meta name="msapplication-TileColor" content="#667eea" />

		<!-- Structured Data -->
		<script type="application/ld+json" set:html={JSON.stringify(finalStructuredData)} />

		<!-- Preload critical resources -->
		<link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossorigin />

		<!-- Performance optimizations -->
		<meta name="robots" content="index, follow" />
		<meta name="googlebot" content="index, follow" />
	</head>
	<body>
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
