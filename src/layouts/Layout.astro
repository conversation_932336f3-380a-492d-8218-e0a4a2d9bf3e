---
export interface Props {
	title?: string;
	description?: string;
	keywords?: string;
}

const {
	title = "Landing Page",
	description = "A modern, customizable landing page built with Astro",
	keywords = "landing page, astro, web development"
} = Astro.props;
---

<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<meta name="keywords" content={keywords} />
		<title>{title}</title>
	</head>
	<body>
		<slot />
	</body>
</html>

<style>
	html,
	body {
		margin: 0;
		width: 100%;
		height: 100%;
	}
</style>
