---
import Layout from '../layouts/Layout.astro';
import Navigation from '../components/Navigation.astro';
import { appLandingConfig } from '../config/landingPage.js';
import { Mail, Phone, MapPin, Clock, Send, MessageCircle } from 'lucide-astro';

const { contact, footer } = appLandingConfig;
---

<Layout title="Contact Us - Authenticator">
  <Navigation />
  
  <main class="contact-page">
    <!-- Hero Section -->
    <section class="contact-hero">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Get in Touch</h1>
          <p class="hero-description">
            Have questions about Authenticator? Need help with your account security? 
            Our team is here to help you stay protected.
          </p>
        </div>
      </div>
    </section>

    <!-- Contact Content -->
    <section class="contact-content">
      <div class="container">
        <div class="contact-grid">
          <!-- Contact Form -->
          <div class="contact-form-section">
            <h2 class="section-title">Send us a Message</h2>
            <form class="contact-form" action="#" method="POST">
              <div class="form-group">
                <label for="name">Full Name</label>
                <input type="text" id="name" name="name" required>
              </div>
              
              <div class="form-group">
                <label for="email">Email Address</label>
                <input type="email" id="email" name="email" required>
              </div>
              
              <div class="form-group">
                <label for="subject">Subject</label>
                <select id="subject" name="subject" required>
                  <option value="">Select a topic</option>
                  <option value="support">Technical Support</option>
                  <option value="security">Security Question</option>
                  <option value="billing">Billing & Pricing</option>
                  <option value="feature">Feature Request</option>
                  <option value="other">Other</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="message">Message</label>
                <textarea id="message" name="message" rows="5" required placeholder="Tell us how we can help you..."></textarea>
              </div>
              
              <button type="submit" class="submit-btn">
                <Send class="btn-icon" />
                Send Message
              </button>
            </form>
          </div>

          <!-- Contact Information -->
          <div class="contact-info-section">
            <h2 class="section-title">Contact Information</h2>
            
            <div class="contact-methods">
              <div class="contact-method">
                <div class="method-icon">
                  <Mail />
                </div>
                <div class="method-content">
                  <h3>Email Support</h3>
                  <p>{contact.email}</p>
                  <span class="method-note">We typically respond within 24 hours</span>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <MessageCircle />
                </div>
                <div class="method-content">
                  <h3>Help Center</h3>
                  <p><a href={contact.supportUrl} target="_blank">Visit Help Center</a></p>
                  <span class="method-note">Find answers to common questions</span>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <Clock />
                </div>
                <div class="method-content">
                  <h3>Support Hours</h3>
                  <p>Monday - Friday: 9:00 AM - 6:00 PM PST</p>
                  <span class="method-note">Emergency security issues: 24/7</span>
                </div>
              </div>

              <div class="contact-method">
                <div class="method-icon">
                  <MapPin />
                </div>
                <div class="method-content">
                  <h3>Office Location</h3>
                  <p>{footer.legal.address}</p>
                  <span class="method-note">By appointment only</span>
                </div>
              </div>
            </div>

            <!-- FAQ Quick Links -->
            <div class="faq-links">
              <h3>Quick Help</h3>
              <ul>
                <li><a href="/#faq">Frequently Asked Questions</a></li>
                <li><a href={contact.supportUrl} target="_blank">Setup Guide</a></li>
                <li><a href={contact.supportUrl} target="_blank">Security Best Practices</a></li>
                <li><a href={contact.supportUrl} target="_blank">Account Recovery</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-section">
          <h4>{footer.appName}</h4>
          <p>{footer.tagline}</p>
        </div>
        
        <div class="footer-section">
          <h4>Quick Links</h4>
          <ul>
            {footer.links.map((link) => (
              <li><a href={link.href}>{link.title}</a></li>
            ))}
          </ul>
        </div>
        
        <div class="footer-section">
          <h4>Follow Us</h4>
          <div class="social-links">
            {footer.social.map((social) => (
              <a href={social.href} title={social.name} target="_blank" rel="noopener noreferrer">
                {social.name}
              </a>
            ))}
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p>&copy; {footer.legal.year} {footer.legal.company}. {footer.copyright}</p>
      </div>
    </div>
  </footer>
</Layout>

<style>
  .contact-page {
    min-height: 100vh;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }

  /* Hero Section */
  .contact-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 120px 0 80px;
    margin-top: 80px;
  }

  .hero-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
  }

  .hero-title {
    font-size: 3rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
  }

  .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
  }

  /* Contact Content */
  .contact-content {
    padding: 80px 0;
    background: white;
  }

  .contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
  }

  .section-title {
    font-size: 2rem;
    font-weight: 700;
    color: #111827;
    margin-bottom: 2rem;
  }

  /* Contact Form */
  .contact-form {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid rgba(102, 126, 234, 0.1);
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
  }

  .form-group input:focus,
  .form-group select:focus,
  .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }

  .submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 0.875rem 2rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  }

  .btn-icon {
    width: 18px;
    height: 18px;
  }

  /* Contact Information */
  .contact-methods {
    margin-bottom: 3rem;
  }

  .contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
  }

  .method-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
  }

  .method-icon svg {
    width: 24px;
    height: 24px;
  }

  .method-content h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 0.5rem;
  }

  .method-content p {
    color: #374151;
    margin-bottom: 0.25rem;
  }

  .method-content a {
    color: #667eea;
    text-decoration: none;
  }

  .method-content a:hover {
    text-decoration: underline;
  }

  .method-note {
    font-size: 0.875rem;
    color: #6b7280;
  }

  /* FAQ Links */
  .faq-links {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
  }

  .faq-links h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 1rem;
  }

  .faq-links ul {
    list-style: none;
    padding: 0;
  }

  .faq-links li {
    margin-bottom: 0.5rem;
  }

  .faq-links a {
    color: #667eea;
    text-decoration: none;
    font-size: 0.95rem;
  }

  .faq-links a:hover {
    text-decoration: underline;
  }

  /* Footer */
  .footer {
    background: #1f2937;
    color: white;
    padding: 3rem 0 1.5rem;
  }

  .footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
  }

  .footer-section h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  .footer-section ul {
    list-style: none;
    padding: 0;
  }

  .footer-section li {
    margin-bottom: 0.5rem;
  }

  .footer-section a {
    color: #d1d5db;
    text-decoration: none;
  }

  .footer-section a:hover {
    color: white;
  }

  .social-links {
    display: flex;
    gap: 1rem;
  }

  .footer-bottom {
    border-top: 1px solid #374151;
    padding-top: 1.5rem;
    text-align: center;
    color: #9ca3af;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .container {
      padding: 0 16px;
    }

    .hero-title {
      font-size: 2.25rem;
    }

    .hero-description {
      font-size: 1.1rem;
    }

    .contact-grid {
      grid-template-columns: 1fr;
      gap: 3rem;
    }

    .contact-form {
      padding: 1.5rem;
    }

    .footer-content {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }

    .social-links {
      justify-content: center;
    }
  }

  @media (max-width: 480px) {
    .container {
      padding: 0 12px;
    }

    .contact-hero {
      padding: 100px 0 60px;
    }

    .hero-title {
      font-size: 1.875rem;
    }

    .contact-content {
      padding: 60px 0;
    }

    .contact-method {
      padding: 1rem;
    }

    .method-icon {
      width: 40px;
      height: 40px;
    }

    .method-icon svg {
      width: 20px;
      height: 20px;
    }
  }
</style>
