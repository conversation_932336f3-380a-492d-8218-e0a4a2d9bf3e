# Image Placeholders

This directory contains placeholder images for the mobile app landing page.

## Required Images:

### App Screenshots (250x500px recommended)
- screenshot-1.png - Main dashboard
- screenshot-2.png - Task creation
- screenshot-3.png - Team collaboration
- screenshot-4.png - Analytics
- screenshot-5.png - Settings

### Hero & Marketing
- app-hero-mockup.png - Hero section app mockup (600x600px)
- app-social-preview.jpg - Social media preview (1200x630px)

### Download Assets
- app-store-badge.png - App Store download badge
- google-play-badge.png - Google Play download badge
- download-qr-code.png - QR code for downloads

### User Avatars (50x50px)
- avatar-1.jpg - User testimonial avatar
- avatar-2.jpg - User testimonial avatar
- avatar-3.jpg - User testimonial avatar
- avatar-4.jpg - User testimonial avatar

## Image Guidelines:

1. **Screenshots**: Use actual app screenshots or high-quality mockups
2. **Hero Image**: Should show the app in a device frame with attractive background
3. **Social Preview**: Include app name, tagline, and key visual elements
4. **Store Badges**: Use official badges from Apple and Google
5. **Avatars**: Professional headshots or placeholder avatars

## Tools for Creating Images:

- **Mockups**: Use tools like Figma, Sketch, or online mockup generators
- **Screenshots**: Take actual screenshots from your app or create mockups
- **Store Badges**: Download official badges from Apple and Google developer sites
- **Social Images**: Use Canva, Figma, or similar tools

## Placeholder Services:

For development, you can use placeholder image services:
- https://picsum.photos/ - Random images
- https://via.placeholder.com/ - Simple placeholder images
- https://unsplash.com/ - High-quality stock photos

Replace these placeholders with your actual app images before going live.
