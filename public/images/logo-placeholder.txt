# Custom App Logo

## Current Configuration
The app is now configured to use a custom logo image located at:
`/public/images/logo.png`

## To Add Your Custom Logo:

1. **Prepare your logo image:**
   - Format: PNG (recommended for transparency) or JPG
   - Size: 512x512px minimum (square aspect ratio)
   - Background: Transparent PNG recommended
   - File size: Under 100KB for optimal loading

2. **Add your logo file:**
   - Save your logo as `logo.png` in the `public/images/` directory
   - Replace this placeholder file

3. **Logo Requirements:**
   - Square aspect ratio (1:1)
   - High contrast for visibility
   - Simple design that works at small sizes
   - Consistent with your app's branding

## Current Placement:
- Navigation bar (48px)
- Hero section (120px on desktop, scales down on mobile)
- Maintains glassmorphism styling and hover effects
- Integrates with purple gradient theme

## Fallback:
If the logo image fails to load, the system will automatically fall back to the default Smartphone icon.

Replace this file with your actual `logo.png` image to see your custom app icon throughout the landing page.
