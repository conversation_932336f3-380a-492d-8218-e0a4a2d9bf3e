<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" stroke="url(#borderGradient)" stroke-width="8"/>
  
  <!-- App Icon Symbol - Task/Productivity Theme -->
  <g transform="translate(256, 256)">
    <!-- Checkmark Circle -->
    <circle cx="-60" cy="-40" r="32" fill="white" opacity="0.9"/>
    <path d="M-72 -40 L-60 -28 L-48 -52" stroke="#667eea" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill="none"/>
    
    <!-- Task Lines -->
    <rect x="-20" y="-60" width="80" height="8" rx="4" fill="white" opacity="0.8"/>
    <rect x="-20" y="-40" width="60" height="8" rx="4" fill="white" opacity="0.6"/>
    <rect x="-20" y="-20" width="70" height="8" rx="4" fill="white" opacity="0.8"/>
    
    <!-- Calendar/Grid -->
    <rect x="-40" y="20" width="80" height="60" rx="8" fill="white" opacity="0.1" stroke="white" stroke-width="2"/>
    <line x1="-20" y1="30" x2="-20" y2="70" stroke="white" opacity="0.3" stroke-width="1"/>
    <line x1="0" y1="30" x2="0" y2="70" stroke="white" opacity="0.3" stroke-width="1"/>
    <line x1="20" y1="30" x2="20" y2="70" stroke="white" opacity="0.3" stroke-width="1"/>
    <line x1="-30" y1="40" x2="30" y2="40" stroke="white" opacity="0.3" stroke-width="1"/>
    <line x1="-30" y1="55" x2="30" y2="55" stroke="white" opacity="0.3" stroke-width="1"/>
    
    <!-- Small dots for calendar days -->
    <circle cx="-25" cy="45" r="2" fill="white" opacity="0.6"/>
    <circle cx="-5" cy="45" r="2" fill="white" opacity="0.6"/>
    <circle cx="15" cy="45" r="2" fill="white" opacity="0.6"/>
    <circle cx="-15" cy="60" r="2" fill="#fbbf24" opacity="0.8"/>
  </g>
  
  <!-- Gradient Definitions -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.1" />
    </linearGradient>
  </defs>
</svg>
